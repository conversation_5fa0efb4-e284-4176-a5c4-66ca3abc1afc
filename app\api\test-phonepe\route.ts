import { NextResponse } from 'next/server';
import { PHONEPE_API, PHONEPE_CONFIG, generateTransactionId, generateSHA256Hash, base64Encode } from '@/config/phonepe';

export async function POST(request: Request) {
  try {
    console.log("Test PhonePe API: Starting direct PhonePe API test");

    // Test with sample data
    const testData = {
      bookingId: "TEST_BOOKING_123",
      userId: "TEST_USER_456",
      amount: 100, // ₹100
      mobileNumber: "9999999999"
    };

    console.log("Test PhonePe API: Using test data:", testData);

    // Generate transaction ID
    const merchantTransactionId = generateTransactionId(testData.bookingId);
    console.log("Generated transaction ID:", merchantTransactionId);

    // Create payment request payload
    // Use webhook.site for testing callbacks since PhonePe doesn't accept localhost
    const testCallbackUrl = "https://webhook.site/unique-url-here"; // Use a webhook testing service
    const testRedirectUrl = "https://webhook.site/unique-url-here"; // Use a webhook testing service

    const paymentRequest = {
      merchantId: PHONEPE_CONFIG.MERCHANT_ID,
      merchantTransactionId: merchantTransactionId,
      merchantUserId: testData.userId.toString(),
      amount: Math.round(testData.amount * 100), // Convert to paise
      redirectUrl: testRedirectUrl,
      redirectMode: 'REDIRECT',
      callbackUrl: testCallbackUrl,
      mobileNumber: testData.mobileNumber.replace(/\D/g, ''),
      paymentInstrument: {
        type: 'PAY_PAGE'
      }
    };

    console.log("Payment request payload:", JSON.stringify(paymentRequest, null, 2));

    // Convert to base64
    const payloadString = JSON.stringify(paymentRequest);
    const base64Payload = base64Encode(payloadString);

    // Generate X-VERIFY header
    const dataToHash = base64Payload + '/pg/v1/pay' + PHONEPE_CONFIG.SALT_KEY;
    const xVerify = await generateSHA256Hash(dataToHash) + '###' + PHONEPE_CONFIG.SALT_INDEX;

    console.log("Base64 payload:", base64Payload);
    console.log("X-Verify:", xVerify);

    // Call PhonePe API directly
    const apiUrl = PHONEPE_CONFIG.IS_TEST_MODE
      ? PHONEPE_API.TEST.INITIATE
      : PHONEPE_API.PROD.INITIATE;

    console.log("Calling PhonePe API:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-VERIFY": xVerify,
      },
      body: JSON.stringify({ request: base64Payload }),
    });

    console.log("PhonePe API response status:", response.status);

    const responseText = await response.text();
    console.log("PhonePe API raw response:", responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (parseError) {
      console.error("Failed to parse PhonePe response:", parseError);
      return NextResponse.json({
        success: false,
        error: "Failed to parse PhonePe response",
        rawResponse: responseText,
        status: response.status
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      phonepeResponse: responseData,
      testData: testData,
      paymentRequest: paymentRequest
    });

  } catch (error: any) {
    console.error("Test PhonePe API: Error:", error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
